import os
import sys
from pathlib import Path

# 动态添加当前目录到 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)
if current_dir not in sys.path:
    sys.path.append(current_dir)

import shutil
import uuid
import jwt
import requests
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from services.minimax_request import minimax_request
from services.script_validator import extract_and_validate_slides
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from services.pdf_service import count_pdf_pages
import logging
from services.clone_voice import clone_voice
from werkzeug.utils import secure_filename
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from services.supabase_storage import get_supabase_client, upload_file_to_supabase

# 导入Celery应用和任务
from celery_worker import celery_app, process_task_celery

app = FastAPI()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 添加CORS中间件，允许前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为您的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 文件夹配置 - 从环境变量获取或使用默认值
PDF_UPLOAD_FOLDER = os.getenv('PDF_UPLOAD_FOLDER', './pdf_upload')
SCRIPT_UPLOAD_FOLDER = os.getenv('SCRIPT_UPLOAD_FOLDER', './script_upload')
RESULT_FOLDER = os.getenv('RESULT_FOLDER', './results')

# 确保文件夹存在
for folder in [PDF_UPLOAD_FOLDER, SCRIPT_UPLOAD_FOLDER, RESULT_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder, exist_ok=True)

logger.info(f"使用文件夹: PDF={PDF_UPLOAD_FOLDER}, SCRIPT={SCRIPT_UPLOAD_FOLDER}, RESULT={RESULT_FOLDER}")

# 任务状态模型
class TaskStatus(BaseModel):
    """任务状态模型"""
    status: str
    result_url: Optional[str] = None
    supabase_url: Optional[str] = None
    error_message: Optional[str] = None
    progress: Optional[int] = None
    message: Optional[str] = None

# 使用Redis存储任务状态
# 在生产环境中，这应该使用Redis或其他持久化存储
# 这里为了简单，仍然使用内存字典
tasks = {}

# JWT 校验依赖
class JWTBearer(HTTPBearer):
    def __init__(self, auto_error: bool = True):
        super(JWTBearer, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(JWTBearer, self).__call__(request)
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(status_code=401, detail="Invalid authentication scheme.")
            return credentials.credentials
        else:
            raise HTTPException(status_code=401, detail="Invalid authorization code.")

# --- Supabase getUser(token) 校验 ---
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")
print("——————SUPABASE_ANON_KEY——————:", SUPABASE_ANON_KEY)

def get_user_from_token(token: str):
    url = f"{SUPABASE_URL}/auth/v1/user"
    headers = {
        "Authorization": f"Bearer {token}",
        "apikey": SUPABASE_ANON_KEY,
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        return resp.json()
    else:
        logger.error(f"Supabase getUser(token) failed: {resp.status_code} {resp.text}")
        raise HTTPException(status_code=401, detail="Invalid or expired token")

def get_current_user_id(token: str = Depends(JWTBearer())):
    user_info = get_user_from_token(token)
    user_id = user_info.get("id")
    if not user_id:
        raise HTTPException(status_code=401, detail="No user id in token")
    return user_id

@app.get("/")
async def root():
    return {"message": "hello from fastapi"}

#最主要的路由，用户点击生成视频之后调用该路由
@app.post("/api/submit-job")
async def submit_job(
    pdf_file: UploadFile = File(...),
    txt_file: UploadFile = File(None),
    voice_option: str = Form(...),
    user_id: str = Depends(get_current_user_id),
):
    try:
        logger.info(f"submit-job: user_id={user_id}")
        # 配额判断和扣减
        supabase = get_supabase_client()
        # 查询配额
        try:
            quota_resp = supabase.table("user_details").select("free_quota").eq("id", user_id).single().execute()
            free_quota = quota_resp.data.get("free_quota", 0) if quota_resp.data else 0
        except Exception as e:
            logger.error(f"查询配额失败: {e}")
            raise HTTPException(status_code=500, detail="查询配额失败")
        if free_quota <= 0:
            logger.info(f"用户 {user_id} 免费配音次数已用完")
            raise HTTPException(status_code=403, detail="Free voiceover limit reached.")
        # 原子性扣减
        try:
            update_result = supabase.table("user_details").update({"free_quota": free_quota - 1}).eq("id", user_id).execute()
            logger.info(f"配额扣减结果: {update_result}")
        except Exception as e:
            logger.error(f"扣减配额失败: {e}")
            raise HTTPException(status_code=500, detail="扣减配额失败")
        logger.info(f"用户 {user_id} 配额扣减成功，剩余: {free_quota - 1}")

        # 生成唯一任务ID
        task_id = str(uuid.uuid4())
        logger.info(f"New job submitted with ID: {task_id}, voice option: {voice_option}")

        # 验证PDF文件
        if pdf_file.filename == '':
            raise HTTPException(status_code=400, detail="No PDF file sent")

        # 保存PDF文件到本地临时目录
        pdf_filename = secure_filename(f"{task_id}_{pdf_file.filename}")
        temp_pdf_filepath = os.path.join(PDF_UPLOAD_FOLDER, pdf_filename)

        try:
            # 先保存到本地临时文件
            with open(temp_pdf_filepath, "wb") as buffer:
                shutil.copyfileobj(pdf_file.file, buffer)
            logger.info(f"PDF file saved locally: {temp_pdf_filepath}")

            # 上传到Supabase
            supabase_result = upload_file_to_supabase(
                file=temp_pdf_filepath,
                bucket_name="upload-pdf",
                file_path=pdf_filename,
                upsert=True
            )

            if not supabase_result["success"]:
                raise Exception(f"Failed to upload PDF to Supabase: {supabase_result.get('error', 'Unknown error')}")

            pdf_supabase_url = supabase_result["public_url"]
            logger.info(f"PDF file uploaded to Supabase: {pdf_supabase_url}")

            # 使用Supabase URL代替本地文件路径
            pdf_filepath = pdf_supabase_url

        except Exception as e:
            logger.error(f"Failed to process PDF file: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to process PDF file: {str(e)}")

        # 保存脚本文件
        script_text = ""
        script_filepath = None

        if txt_file and txt_file.filename:
            script_filename = secure_filename(f"{task_id}_{txt_file.filename}")
            temp_script_filepath = os.path.join(SCRIPT_UPLOAD_FOLDER, script_filename)

            try:
                script_content_bytes = await txt_file.read()
                script_text = script_content_bytes.decode('utf-8')

                # 先保存到本地临时文件
                with open(temp_script_filepath, "w", encoding='utf-8') as f:
                    f.write(script_text)
                logger.info(f"Script file saved locally: {temp_script_filepath}")

                # 上传到Supabase
                supabase_result = upload_file_to_supabase(
                    file=temp_script_filepath,
                    bucket_name="upload-script",
                    file_path=script_filename,
                    upsert=True
                )

                if not supabase_result["success"]:
                    raise Exception(f"Failed to upload script to Supabase: {supabase_result.get('error', 'Unknown error')}")

                script_supabase_url = supabase_result["public_url"]
                logger.info(f"Script file uploaded to Supabase: {script_supabase_url}")

                # 使用Supabase URL代替本地文件路径
                script_filepath = script_supabase_url

            except Exception as e:
                logger.error(f"Failed to process script file: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to process script file: {str(e)}")

        # 创建任务记录 - 初始状态为排队中
        task_status = TaskStatus(
            status="queued",
            progress=0,
            message="Task queued, waiting for processing"
        )
        # 使用model_dump代替已弃用的dict方法
        tasks[task_id] = task_status.model_dump()

        # 启动Celery任务
        logger.info(f"启动Celery任务处理: {task_id}")
        celery_task = process_task_celery.delay(
            task_id=task_id,
            pdf_path=pdf_filepath,
            script_path=script_filepath,
            voice_option=voice_option,
            user_id=user_id
        )

        # 记录Celery任务ID，便于后续查询
        tasks[task_id]["celery_task_id"] = celery_task.id
        logger.info(f"Celery任务已启动，ID: {celery_task.id}")

        # 返回任务ID给前端
        response = {
            "task_id": task_id,
            "message": "Files uploaded successfully. Processing started.",
            "status": "queued"
        }

        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in submit_job: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")

@app.get("/api/status/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态

    首先检查本地缓存，如果任务已完成或失败，直接返回结果
    否则，查询Celery任务状态并更新本地缓存
    """
    # 检查任务是否存在于本地缓存
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks[task_id]

    # 如果任务已完成或失败，直接返回缓存的结果
    if task.get("status") in ["completed", "failed"]:
        return {
            "status": task["status"],
            "result_url": task.get("result_url"),
            "supabase_url": task.get("supabase_url"),
            "error_message": task.get("error_message"),
            "progress": task.get("progress", 100 if task["status"] == "completed" else 0)
        }

    # 否则，查询Celery任务状态
    celery_task_id = task.get("celery_task_id")
    if not celery_task_id:
        logger.warning(f"任务 {task_id} 没有关联的Celery任务ID")
        return {
            "status": task["status"],
            "progress": task.get("progress", 0),
            "message": "Task status unknown"
        }

    # 从Celery获取任务状态
    celery_task = process_task_celery.AsyncResult(celery_task_id)
    logger.info(f"Celery任务 {celery_task_id} 状态: {celery_task.status}")

    # 根据Celery任务状态更新本地缓存
    if celery_task.status == 'SUCCESS':
        # 任务成功完成
        result = celery_task.result
        logger.info(f"Celery任务结果: {result}")

        # 更新本地缓存
        tasks[task_id] = {
            "status": "completed",
            "result_url": result.get("result_url"),
            "supabase_url": result.get("supabase_url"),
            "progress": 100,
            "message": result.get("message", "Task completed successfully")
        }

        # 返回更新后的状态
        return {
            "status": "completed",
            "result_url": result.get("result_url"),
            "supabase_url": result.get("supabase_url"),
            "progress": 100
        }

    elif celery_task.status == 'FAILURE':
        # 任务失败
        error_info = str(celery_task.result) if celery_task.result else "Unknown error"
        logger.error(f"Celery任务失败: {error_info}")

        # 更新本地缓存
        tasks[task_id] = {
            "status": "failed",
            "error_message": error_info,
            "progress": 0
        }

        # 返回失败状态
        return {
            "status": "failed",
            "error_message": error_info,
            "progress": 0
        }

    elif celery_task.status == 'PENDING':
        # 任务等待中
        tasks[task_id]["status"] = "queued"
        tasks[task_id]["progress"] = 5

        return {
            "status": "queued",
            "progress": 5,
            "message": "Task is pending"
        }

    elif celery_task.status == 'STARTED' or celery_task.status == 'PROCESSING':
        # 任务处理中
        tasks[task_id]["status"] = "processing"

        # 尝试获取进度信息
        if celery_task.info and isinstance(celery_task.info, dict):
            progress = celery_task.info.get('progress', 50)
            message = celery_task.info.get('message', 'Processing task...')
            tasks[task_id]["progress"] = progress
            tasks[task_id]["message"] = message

            return {
                "status": "processing",
                "progress": progress,
                "message": message
            }
        else:
            # 没有详细进度信息，返回默认值
            return {
                "status": "processing",
                "progress": 50,
                "message": "Processing task..."
            }

    else:
        # 其他状态
        return {
            "status": task["status"],
            "progress": task.get("progress", 0),
            "message": f"Task status: {celery_task.status}"
        }


# 保留原有的上传接口以兼容 已弃用
@app.post("/upload")
async def upload(
    pdf_file: UploadFile = File(...),
    script_file: UploadFile = File(...)
):
    if pdf_file.filename == '':
        raise HTTPException(status_code=400, detail="No pdf file sent")
    if script_file.filename == '':
        raise HTTPException(status_code=400, detail="No script file sent")

    pdf_filename = secure_filename(pdf_file.filename)
    script_filename = secure_filename(script_file.filename)
    pdf_filepath = os.path.join(PDF_UPLOAD_FOLDER, pdf_filename)
    script_filepath = os.path.join(SCRIPT_UPLOAD_FOLDER, script_filename)

    try:
        with open(pdf_filepath, "wb") as buffer:
            shutil.copyfileobj(pdf_file.file, buffer)
        print(f"{pdf_filepath} saved")
    except Exception as e:
        print(f"fail to save file: {e}")
        raise HTTPException(status_code=500, detail="Failed to save pdf file")

    script_text = ""
    try:
        script_content_bytes = await script_file.read()
        script_text = script_content_bytes.decode('utf-8')
    except Exception as e:
        print(f"fail to read script file: {e}")
        raise HTTPException(status_code=500, detail="Failed to read script file")

    try:
        with open(script_filepath, "w", encoding='utf-8') as f:
            f.write(script_text)
        print(f"{script_filepath} saved")
    except Exception as e:
        print(f"fail to save file: {e}")
        raise HTTPException(status_code=500, detail="Failed to save script file")

    return {"message": "pdf and script files upload success"}

@app.get("/minimax")
def minimax(text: str, save_floder: str):
    minimax_request(text, save_floder)
    return {"message": "minimax request success"}



# 新增接口
# 音色克隆API
@app.post("/api/clone-voice")
async def api_clone_voice(
    audio_file: UploadFile = File(...),
    preview_text: str = Form(...),
    voice_id: Optional[str] = Form(None)
):
    """
    克隆音色并生成试听音频

    Args:
        audio_file: 用户上传的音频文件
        preview_text: 试听文本
        voice_id: 可选的音色ID

    Returns:
        包含试听URL和音色ID的JSON响应
    """
    try:
        logger.info(f"接收到音色克隆请求，文件名: {audio_file.filename}, 音色ID: {voice_id}")

        # 验证文件是否存在
        if not audio_file or not audio_file.file:
            logger.error("未提供音频文件或文件对象为空")
            raise HTTPException(status_code=400, detail="请上传音频文件")

        # 验证文件类型
        filename = audio_file.filename.lower() if audio_file.filename else ""
        if not (filename.endswith('.mp3') or filename.endswith('.wav') or filename.endswith('.m4a')):
            logger.error(f"不支持的文件类型: {filename}")
            raise HTTPException(status_code=400, detail="只支持MP3、WAV或M4A格式的音频文件")

        # 验证预览文本
        if not preview_text or len(preview_text.strip()) < 5:
            logger.error(f"预览文本太短: '{preview_text}'")
            raise HTTPException(status_code=400, detail="预览文本太短，请输入至少5个字符")

        # 保存文件到临时位置以便调试
        temp_file_path = f"temp_{uuid.uuid4()}.{filename.split('.')[-1]}"
        try:
            # 保存上传的文件内容
            content = await audio_file.read()
            with open(temp_file_path, "wb") as f:
                f.write(content)
            logger.info(f"已保存临时文件: {temp_file_path}")

            # 重置文件指针
            await audio_file.seek(0)
        except Exception as e:
            logger.error(f"保存临时文件失败: {str(e)}")
            # 继续处理，不中断流程

        # 调用克隆函数
        logger.info("开始调用克隆函数")

        # 使用临时文件路径而不是文件对象
        # 这样可以确保文件名和扩展名正确传递给API
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            logger.info(f"使用临时文件路径调用克隆函数: {temp_file_path}")
            success, demo_url, response_data = clone_voice(
                temp_file_path,  # 使用文件路径而不是文件对象
                preview_text,
                voice_id
            )
        else:
            # 如果临时文件创建失败，退回到使用文件对象
            logger.info("使用文件对象调用克隆函数")
            success, demo_url, response_data = clone_voice(
                audio_file,  # 传递整个UploadFile对象而不仅仅是file属性
                preview_text,
                voice_id
            )

        # 记录响应数据
        logger.info(f"克隆函数返回: success={success}, response_data={response_data}")

        if success and demo_url:
            result = {
                "success": True,
                "demo_audio_url": demo_url,
                "voice_id": response_data.get("voice_id"),
                "file_id": response_data.get("file_id")
            }
            logger.info(f"音色克隆成功: {result}")
            return result
        else:
            error_msg = response_data.get("error", "音色克隆失败，请稍后重试")
            logger.error(f"音色克隆失败: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"音色克隆过程中发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
    finally:
        # 清理临时文件
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                logger.info(f"已删除临时文件: {temp_file_path}")
        except Exception as e:
            logger.error(f"删除临时文件失败: {str(e)}")

# 添加静态文件服务，用于提供结果文件

# 自定义路由处理下载请求 - 使用与前端相同的路径结构
@app.get("/results/{file_path:path}/download")
async def download_file(file_path: str):
    file_location = os.path.join(RESULT_FOLDER, file_path)
    if not os.path.isfile(file_location):
        raise HTTPException(status_code=404, detail="File not found")

    # 设置Content-Disposition头，强制浏览器下载文件而不是在页面中打开
    filename = os.path.basename(file_path)
    return FileResponse(
        path=file_location,
        filename=filename,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

# 静态文件服务，用于预览和常规访问
app.mount("/results", StaticFiles(directory=RESULT_FOLDER), name="results")

if __name__=="__main__":
    import uvicorn
    # logger.info(f"the version of --- change way to getuser()!")
    uvicorn.run("main:app", host="0.0.0.0", port=8000)
