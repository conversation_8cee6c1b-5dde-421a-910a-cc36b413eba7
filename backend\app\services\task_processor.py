import os
import time
import asyncio
import logging
import concurrent.futures
from .minimax_request import minimax_request
from .script_validator import extract_and_validate_slides
from .pdf_service import count_pdf_pages
from .pdf2video import generate_video_from_pdf_and_audio
from .supabase_storage import upload_file_to_supabase

# 配置日志
logger = logging.getLogger(__name__)

async def generate_slide_audio(slide_text, slide_index, task_id, voice_settings, audio_folder):
    """
    为单个幻灯片生成音频文件

    参数:
    slide_text (str): 幻灯片文本内容
    slide_index (int): 幻灯片索引（从1开始）
    task_id (str): 任务ID
    voice_settings (dict): 语音设置
    audio_folder (str): 音频文件保存文件夹

    返回:
    str: 生成的音频文件路径
    """
    try:
        # 生成音频文件名
        output_filename = f"slide{slide_index}"

        # 调用MiniMax API生成音频
        audio_path = minimax_request(slide_text, audio_folder, voice_settings, output_filename)

        logger.info(f"为幻灯片 {slide_index} 生成音频文件: {audio_path}")
        return audio_path
    except Exception as e:
        logger.error(f"为幻灯片 {slide_index} 生成音频失败: {str(e)}")
        raise e

def generate_slide_audio_sync(slide_text, slide_index, task_id, voice_settings, audio_folder):
    """
    为单个幻灯片生成音频文件（同步版本）

    参数:
    slide_text (str): 幻灯片文本内容
    slide_index (int): 幻灯片索引（从1开始）
    task_id (str): 任务ID
    voice_settings (dict): 语音设置
    audio_folder (str): 音频文件保存文件夹

    返回:
    str: 生成的音频文件路径
    """
    try:
        # 生成音频文件名
        output_filename = f"slide{slide_index}"

        # 调用MiniMax API生成音频
        audio_path = minimax_request(slide_text, audio_folder, voice_settings, output_filename)

        logger.info(f"为幻灯片 {slide_index} 生成音频文件: {audio_path}")
        return audio_path
    except Exception as e:
        logger.error(f"为幻灯片 {slide_index} 生成音频失败: {str(e)}")
        raise e

async def process_task(task_id, pdf_path, script_path, voice_option, tasks_dict):
    """
    处理任务的异步函数
    """
    try:
        # 更新任务状态为处理中
        tasks_dict[task_id]["status"] = "processing"

        # 获取PDF页数
        pdf_page_count = count_pdf_pages(pdf_path)
        if pdf_page_count is None:
            raise Exception("Failed to count PDF pages")

        # 处理脚本
        script_text = ""
        if script_path:
            with open(script_path, 'r', encoding='utf-8') as f:
                script_text = f.read()

        # 验证脚本与PDF页数是否匹配
        is_valid, error_msg, slide_contents = extract_and_validate_slides(script_text, pdf_page_count)
        if not is_valid:
            raise Exception(f"Script validation failed: {error_msg}")

        # 设置语音参数
        voice_settings = get_voice_settings(voice_option)

        # 创建结果目录（如果不存在）
        result_folder = "./results"
        if not os.path.exists(result_folder):
            os.makedirs(result_folder)

        # 创建音频文件夹
        audio_folder = os.path.join(result_folder, f"{task_id}_audio")
        if not os.path.exists(audio_folder):
            os.makedirs(audio_folder)

        logger.info(f"开始为 {len(slide_contents)} 页幻灯片生成音频...")

        # 为每页幻灯片生成音频文件
        audio_tasks = []
        for i, slide_text in enumerate(slide_contents):
            slide_index = i + 1
            audio_task = generate_slide_audio(slide_text, slide_index, task_id, voice_settings, audio_folder)
            audio_tasks.append(audio_task)

        # 并发执行音频生成任务
        audio_paths = await asyncio.gather(*audio_tasks, return_exceptions=True)

        # 检查是否有任务失败
        for i, result in enumerate(audio_paths):
            if isinstance(result, Exception):
                raise Exception(f"Failed to generate audio for slide {i+1}: {str(result)}")

        logger.info(f"所有幻灯片音频生成完成，开始生成视频...")

        # 生成视频文件名
        video_filename = f"{task_id}_video.mp4"
        video_path = os.path.join(result_folder, video_filename)

        # 检查PDF文件是否存在
        if not os.path.exists(pdf_path):
            raise Exception(f"PDF文件不存在: {pdf_path}")

        # 检查音频文件夹是否存在
        if not os.path.exists(audio_folder):
            raise Exception(f"音频文件夹不存在: {audio_folder}")

        # 检查音频文件夹中是否有文件
        audio_files = os.listdir(audio_folder)
        logger.info(f"音频文件夹中的文件: {audio_files}")

        if not audio_files:
            raise Exception(f"音频文件夹为空: {audio_folder}")

        # 调用PDF2Video生成视频
        logger.info(f"开始生成视频，参数: PDF={pdf_path}, 音频文件夹={audio_folder}, 输出路径={video_path}")
        final_video_path = generate_video_from_pdf_and_audio(
            pdf_path=pdf_path,
            audio_folder=audio_folder,
            output_video_path=video_path,
            rendering_dpi=200,
            fps=15,
            clean_temp_files=True
        )

        if final_video_path is None:
            raise Exception("Failed to generate video")

        # 检查生成的视频文件是否存在
        if not os.path.exists(final_video_path):
            raise Exception(f"生成的视频文件不存在: {final_video_path}")

        logger.info(f"视频生成成功: {final_video_path}, 文件大小: {os.path.getsize(final_video_path)} 字节")

        # 尝试将视频上传到Supabase
        try:
            # 上传到Supabase
            supabase_result = upload_file_to_supabase(
                file=final_video_path,
                bucket_name="result-video",  # 使用result-video存储桶
                file_path=video_filename,
                upsert=True,  # 覆盖同名文件
                file_options={
                    "content-type":"video/mp4"
                }
            )

            if supabase_result["success"]:
                logger.info(f"视频已上传到Supabase: {supabase_result['public_url']}")
                # 更新任务状态，包含Supabase URL
                tasks_dict[task_id]["status"] = "completed"
                tasks_dict[task_id]["result_url"] = f"/results/{video_filename}"  # 本地路径
                tasks_dict[task_id]["supabase_url"] = supabase_result["public_url"]  # Supabase URL
            else:
                logger.warning(f"视频上传到Supabase失败: {supabase_result.get('error', '未知错误')}")
                # 仍然将任务标记为完成，但只使用本地URL
                tasks_dict[task_id]["status"] = "completed"
                tasks_dict[task_id]["result_url"] = f"/results/{video_filename}"
        except Exception as e:
            logger.warning(f"尝试上传视频到Supabase时出错: {str(e)}")
            # 仍然将任务标记为完成，但只使用本地URL
            tasks_dict[task_id]["status"] = "completed"
            tasks_dict[task_id]["result_url"] = f"/results/{video_filename}"

        logger.info(f"任务 {task_id} 处理完成，视频已保存到 {final_video_path}")

    except Exception as e:
        # 处理错误
        logger.error(f"Task processing error: {str(e)}")
        tasks_dict[task_id]["status"] = "failed"
        tasks_dict[task_id]["error_message"] = str(e)

def process_task_sync(task_id, pdf_path, script_path, voice_settings):
    """
    处理任务的同步函数，用于Celery任务

    参数:
        task_id: 任务ID
        pdf_path: PDF文件路径或Supabase URL
        script_path: 脚本文件路径或Supabase URL
        voice_settings: 语音设置

    返回:
        包含处理结果的字典
    """
    try:
        logger.info(f"开始同步处理任务 {task_id}")

        # 创建临时目录
        temp_dir = os.path.join(os.getenv('RESULT_FOLDER', "./results"), f"{task_id}_temp")
        os.makedirs(temp_dir, exist_ok=True)

        # 检查PDF路径是否为Supabase URL
        local_pdf_path = pdf_path
        if pdf_path.startswith('http'):
            logger.info(f"从Supabase下载PDF文件: {pdf_path}")
            # 从URL中提取文件名
            pdf_filename = os.path.basename(pdf_path.split('?')[0])
            local_pdf_path = os.path.join(temp_dir, pdf_filename)

            # 从Supabase下载文件
            from .supabase_storage import download_file_from_supabase
            download_result = download_file_from_supabase(
                url=pdf_path,
                destination=local_pdf_path
            )

            if not download_result["success"]:
                raise Exception(f"Failed to download PDF from Supabase: {download_result.get('error', 'Unknown error')}")

            logger.info(f"PDF文件已下载到本地: {local_pdf_path}")

        # 获取PDF页数
        pdf_page_count = count_pdf_pages(local_pdf_path)
        if pdf_page_count is None:
            raise Exception("Failed to count PDF pages")

        # 处理脚本
        script_text = ""
        if script_path:
            # 检查脚本路径是否为Supabase URL
            if script_path.startswith('http'):
                logger.info(f"从Supabase下载脚本文件: {script_path}")
                # 从URL中提取文件名
                script_filename = os.path.basename(script_path.split('?')[0])
                local_script_path = os.path.join(temp_dir, script_filename)

                # 从Supabase下载文件
                from .supabase_storage import download_file_from_supabase
                download_result = download_file_from_supabase(
                    url=script_path,
                    destination=local_script_path
                )

                if not download_result["success"]:
                    raise Exception(f"Failed to download script from Supabase: {download_result.get('error', 'Unknown error')}")

                logger.info(f"脚本文件已下载到本地: {local_script_path}")

                with open(local_script_path, 'r', encoding='utf-8') as f:
                    script_text = f.read()
            else:
                # 本地文件路径
                with open(script_path, 'r', encoding='utf-8') as f:
                    script_text = f.read()

        # 验证脚本与PDF页数是否匹配
        is_valid, error_msg, slide_contents = extract_and_validate_slides(script_text, pdf_page_count)
        if not is_valid:
            raise Exception(f"Script validation failed: {error_msg}")

        # 创建结果目录（如果不存在）- 从环境变量获取或使用默认值
        result_folder = os.getenv('RESULT_FOLDER', "./results")
        if not os.path.exists(result_folder):
            os.makedirs(result_folder, exist_ok=True)

        # 创建音频文件夹
        audio_folder = os.path.join(result_folder, f"{task_id}_audio")
        if not os.path.exists(audio_folder):
            os.makedirs(audio_folder, exist_ok=True)

        logger.info(f"使用结果目录: {result_folder}, 音频文件夹: {audio_folder}")

        logger.info(f"开始为 {len(slide_contents)} 页幻灯片生成音频...")

        # 为每页幻灯片生成音频文件（使用线程池并发处理）
        audio_paths = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, len(slide_contents))) as executor:
            futures = []
            for i, slide_text in enumerate(slide_contents):
                slide_index = i + 1
                future = executor.submit(
                    generate_slide_audio_sync,
                    slide_text,
                    slide_index,
                    task_id,
                    voice_settings,
                    audio_folder
                )
                futures.append(future)

            # 等待所有任务完成
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                try:
                    audio_path = future.result()
                    audio_paths.append(audio_path)
                    logger.info(f"幻灯片 {i+1} 音频生成完成: {audio_path}")
                except Exception as e:
                    logger.error(f"幻灯片 {i+1} 音频生成失败: {str(e)}")
                    raise Exception(f"Failed to generate audio for slide {i+1}: {str(e)}")

        logger.info(f"所有幻灯片音频生成完成，开始生成视频...")

        # 生成视频文件名
        video_filename = f"{task_id}_video.mp4"
        video_path = os.path.join(result_folder, video_filename)

        # 检查PDF文件是否存在
        if not os.path.exists(local_pdf_path):
            raise Exception(f"PDF文件不存在: {local_pdf_path}")

        # 检查音频文件夹是否存在
        if not os.path.exists(audio_folder):
            raise Exception(f"音频文件夹不存在: {audio_folder}")

        # 检查音频文件夹中是否有文件
        audio_files = os.listdir(audio_folder)
        logger.info(f"音频文件夹中的文件: {audio_files}")

        if not audio_files:
            raise Exception(f"音频文件夹为空: {audio_folder}")

        # 调用PDF2Video生成视频
        logger.info(f"开始生成视频，参数: PDF={local_pdf_path}, 音频文件夹={audio_folder}, 输出路径={video_path}")
        final_video_path = generate_video_from_pdf_and_audio(
            pdf_path=local_pdf_path,
            audio_folder=audio_folder,
            output_video_path=video_path,
            rendering_dpi=200,
            fps=15,
            clean_temp_files=True
        )

        if final_video_path is None:
            raise Exception("Failed to generate video")

        # 检查生成的视频文件是否存在
        if not os.path.exists(final_video_path):
            raise Exception(f"生成的视频文件不存在: {final_video_path}")

        logger.info(f"视频生成成功: {final_video_path}, 文件大小: {os.path.getsize(final_video_path)} 字节")

        # 尝试将视频上传到Supabase
        try:
            # 上传到Supabase
            supabase_result = upload_file_to_supabase(
                file=final_video_path,
                bucket_name="result-video",  # 使用result-video存储桶
                file_path=video_filename,
                upsert=True,  # 覆盖同名文件
                file_options={
                    "content-type":"video/mp4"
                }
            )

            if supabase_result["success"]:
                logger.info(f"视频已上传到Supabase: {supabase_result['public_url']}")
                # 返回成功结果
                return {
                    "success": True,
                    "result_url": f"/results/{video_filename}",  # 本地路径
                    "supabase_url": supabase_result["public_url"]  # Supabase URL
                }
            else:
                logger.warning(f"视频上传到Supabase失败: {supabase_result.get('error', '未知错误')}")
                # 仍然返回成功，但只使用本地URL
                return {
                    "success": True,
                    "result_url": f"/results/{video_filename}"
                }
        except Exception as e:
            logger.warning(f"尝试上传视频到Supabase时出错: {str(e)}")
            # 仍然返回成功，但只使用本地URL
            return {
                "success": True,
                "result_url": f"/results/{video_filename}"
            }

    except Exception as e:
        # 处理错误
        logger.error(f"Task processing error: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }

def get_voice_settings(voice_option):
    """
    根据选择的语音选项返回相应的语音设置
    """
    # 预设音色设置
    voice_settings = {
        "male-qn-qingse": {
            "voice_id": "male-qn-qingse",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0
        },
        "male-qn-jingying": {
            "voice_id": "male-qn-jingying",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0
        },
        "female-shaonv": {
            "voice_id": "female-shaonv",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0
        },
        "female-yujie": {
            "voice_id": "female-yujie",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0
        }
    }

    # 检查是否是预设音色
    if voice_option in voice_settings:
        return voice_settings[voice_option]

    # 如果不是预设音色，则假设是克隆音色ID
    logger.info(f"使用克隆音色ID: {voice_option}")

    # 返回克隆音色的设置
    return {
        "voice_id": voice_option,  # 直接使用voice_option作为voice_id
        "speed": 1.0,
        "vol": 1.0,
        "pitch": 0
    }
