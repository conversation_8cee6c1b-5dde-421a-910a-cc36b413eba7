import os
import logging
from typing import Optional, Union, BinaryIO
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client, Client

# 配置日志
logger = logging.getLogger(__name__)

def load_supabase_env():
    # 优先用系统环境变量
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')

    if not supabase_url or not supabase_key:
        # 多路径查找 .env
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        root_dir = os.path.dirname(parent_dir)
        possible_env_paths = [
            os.path.join(current_dir, '.env'),
            os.path.join(parent_dir, '.env'),
            os.path.join(root_dir, '.env'),
        ]
        for env_path in possible_env_paths:
            if os.path.isfile(env_path):
                load_dotenv(env_path)
                logger.info(f"Loaded .env from {env_path}")
                break
        # 再次尝试获取
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')

    missing_vars = []
    if not supabase_url:
        missing_vars.append('SUPABASE_URL')
    if not supabase_key:
        missing_vars.append('SUPABASE_SERVICE_ROLE_KEY')
    if missing_vars:
        error_msg = f"Supabase环境变量未设置: {', '.join(missing_vars)}"
        logger.error(error_msg)
        raise ValueError(error_msg)
    return supabase_url, supabase_key

# 模块导入时自动检测环境变量
try:
    _supabase_url, _supabase_key = load_supabase_env()
    logger.info("Successfully loaded Supabase environment variables")
    logger.info(f"SUPABASE_URL:{_supabase_url}")
    logger.info(f"the version of --- change way to getuser()!")
except Exception as e:
    logger.error(f"Failed to load Supabase environment variables: {e}")
    raise

def get_supabase_client() -> Client:
    """
    获取Supabase客户端实例

    从环境变量中读取Supabase URL和密钥，创建并返回Supabase客户端

    Returns:
        Client: Supabase客户端实例

    Raises:
        ValueError: 如果环境变量未设置
    """
    return create_client(_supabase_url, _supabase_key)

def upload_file_to_supabase(
    file: Union[str, Path, BinaryIO],
    bucket_name: str,
    file_path: str,
    cache_control: str = "3600",
    upsert: bool = False
) -> dict:
    """
    上传文件到Supabase Storage

    Args:
        file: 文件路径或已打开的文件对象
        bucket_name: Supabase存储桶名称
        file_path: 文件在Supabase中的存储路径  如果要创建文件夹，直接写路径即可："/foldername/filename"
        cache_control: 缓存控制设置，默认为3600秒
        upsert: 是否覆盖已存在的文件，默认为False

    Returns:
        dict: Supabase上传响应

    Raises:
        ValueError: 如果参数无效
        Exception: 如果上传过程中发生错误
    """
    try:
        # 验证参数
        if not bucket_name or not file_path:
            raise ValueError("bucket_name和file_path不能为空")

        # 获取Supabase客户端
        supabase = get_supabase_client()

        # 处理文件参数
        if isinstance(file, (str, Path)):
            # 如果是文件路径，打开文件
            file_path_obj = Path(file)
            if not file_path_obj.exists():
                raise ValueError(f"文件不存在: {file}")

            logger.info(f"正在上传文件: {file_path_obj} 到 {bucket_name}/{file_path}")
            with open(file_path_obj, "rb") as f:
                response = supabase.storage.from_(bucket_name).upload(
                    file=f,
                    path=file_path,
                    file_options={"cache-control": cache_control, "upsert": str(upsert).lower()}
                )
        else:
            # 如果是文件对象，直接使用
            logger.info(f"正在上传文件对象到 {bucket_name}/{file_path}")
            response = supabase.storage.from_(bucket_name).upload(
                file=file,
                path=file_path,
                file_options={"cache-control": cache_control, "upsert": str(upsert).lower()}
            )

        logger.info(f"文件上传成功: {file_path}")

        # 获取文件的公共URL
        public_url = supabase.storage.from_(bucket_name).get_public_url(file_path)

        # 返回上传响应和公共URL
        return {
            "success": True,
            "response": response,
            "public_url": public_url
        }

    except Exception as e:
        logger.error(f"上传文件到Supabase失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def download_file_from_supabase(url: str, destination: str) -> dict:
    """
    从Supabase Storage下载文件

    Args:
        url: Supabase文件的URL
        destination: 下载文件的本地保存路径

    Returns:
        dict: 下载结果
    """
    try:
        # 验证参数
        if not url or not destination:
            raise ValueError("url和destination不能为空")

        # 获取Supabase客户端
        supabase = get_supabase_client()

        # 从URL中提取bucket和path
        # URL格式: https://{project_ref}.supabase.co/storage/v1/object/public/{bucket_name}/{file_path}
        parts = url.split('/storage/v1/object/public/')
        if len(parts) != 2:
            raise ValueError(f"无效的Supabase URL格式: {url}")

        bucket_and_path = parts[1].split('?')[0]  # 移除查询参数
        bucket_parts = bucket_and_path.split('/', 1)
        if len(bucket_parts) != 2:
            raise ValueError(f"无法从URL提取bucket和path: {url}")

        bucket_name = bucket_parts[0]
        file_path = bucket_parts[1]

        logger.info(f"从Supabase下载文件: bucket={bucket_name}, path={file_path}")

        # 创建目标目录（如果不存在）
        os.makedirs(os.path.dirname(os.path.abspath(destination)), exist_ok=True)

        # 下载文件
        with open(destination, 'wb') as f:
            response = supabase.storage.from_(bucket_name).download(file_path)
            f.write(response)

        logger.info(f"文件下载成功: {destination}")

        return {
            "success": True,
            "destination": destination
        }

    except Exception as e:
        logger.error(f"从Supabase下载文件失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# 示例用法
if __name__ == "__main__":
    # 上传文件示例
    result = upload_file_to_supabase(
        file="for_voice_clone.m4a",
        bucket_name="result-dubbing",
        file_path="/1/for_voice_clone.m4a"
    )

    if result["success"]:
        print(f"文件上传成功: {result['public_url']}")
    else:
        print(f"文件上传失败: {result['error']}")
