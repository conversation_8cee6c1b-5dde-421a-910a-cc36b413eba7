#!/usr/bin/env python3
"""
MiniMax API 简单测试脚本
直接在代码中设置 API 密钥进行测试
"""

import requests
import json

# ⚠️ 请在这里填入你的真实配置
GROUP_ID="1908770824672788877"
API_KEY="**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

def test_minimax():
    """测试 MiniMax API"""
    
    print("=== MiniMax API 简单测试 ===")
    print(f"GROUP_ID: {GROUP_ID}")
    print(f"API_KEY: {API_KEY}")
    
    # 构建请求
    url = f"https://api.minimax.chat/v1/t2a_v2?GroupId={GROUP_ID}"
    
    payload = json.dumps({
        "model": "speech-02-turbo",
        "text": "你好，这是一个测试。",
        "stream": False,
        "voice_setting": {
            "voice_id": "male-qn-qingse",
            "speed": 1,
            "vol": 1,
            "pitch": 0
        },
        "audio_setting": {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3",
            "channel": 1
        }
    })
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    print(f"\n📡 发送请求到: {url}")
    
    try:
        response = requests.post(url, headers=headers, data=payload, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            parsed_json = response.json()
            print(f"📄 完整响应: {json.dumps(parsed_json, indent=2, ensure_ascii=False)}")
            
            # 检查是否有错误
            if "base_resp" in parsed_json:
                base_resp = parsed_json["base_resp"]
                if base_resp.get("status_code", 0) != 0:
                    print(f"❌ API 错误: {base_resp}")
                    return
            
            # 检查音频数据
            if "data" in parsed_json and parsed_json["data"]:
                if "audio" in parsed_json["data"]:
                    audio_hex = parsed_json["data"]["audio"]
                    if audio_hex:
                        audio_bytes = bytes.fromhex(audio_hex)
                        
                        # 保存音频文件
                        with open('test_simple.mp3', 'wb') as f:
                            f.write(audio_bytes)
                        
                        print(f"✅ 成功! 音频大小: {len(audio_bytes)} 字节")
                        print(f"📁 已保存为: test_simple.mp3")
                    else:
                        print("❌ 音频数据为空")
                else:
                    print("❌ 没有音频字段")
            else:
                print("❌ 没有数据字段")
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_minimax()
