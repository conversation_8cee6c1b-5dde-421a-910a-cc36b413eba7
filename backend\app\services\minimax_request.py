import json
import time
from typing import Iterator
import requests
import os
from dotenv import load_dotenv
import logging


logging.basicConfig(level=logging.INFO,
                    format='%(levelname)s - %(filename)s:%(lineno)d - %(message)s')
logger = logging.getLogger(__name__)


# 优先直接从系统环境变量获取 针对线上部署环境，没有.env文件的问题
group_id = os.environ.get('GROUP_ID')
api_key = os.environ.get('MINIMAX_APIKEY')

if group_id is None or api_key is None:
    logger.info("Some environment variables are missing, trying to load from .env file...")
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取父目录（app/）
    parent_dir = os.path.dirname(current_dir)
    # 获取根目录（backend/）
    root_dir = os.path.dirname(parent_dir)

    # 尝试多个可能的 .env 文件位置
    possible_env_paths = [
        os.path.join(current_dir, '.env'),  # services/.env
        os.path.join(parent_dir, '.env'),   # app/.env
        os.path.join(root_dir, '.env'),     # backend/.env
    ]

    # 尝试加载第一个存在的 .env 文件
    for env_path in possible_env_paths:
        if os.path.isfile(env_path):
            load_dotenv(env_path)
            logger.info(f"Loaded .env from {env_path}")
            break

    # 再次尝试获取环境变量
    group_id = os.environ.get('GROUP_ID')
    api_key = os.environ.get('MINIMAX_APIKEY')

# 检查是否成功获取所有必要的环境变量
missing_vars = []
if group_id is None:
    missing_vars.append('GROUP_ID')
if api_key is None:
    missing_vars.append('MINIMAX_APIKEY')

if missing_vars:
    error_msg = f"The following environment variables are not set: {', '.join(missing_vars)}"
    logger.error(error_msg)
    raise ValueError(error_msg)

# 记录成功获取环境变量
logger.info("#####Successfully loaded all required environment variables: minimax#####")

file_format = 'mp3'  # 支持 mp3/pcm/flac



def build_tts_headers() -> dict:
    """构建非流式TTS请求头"""
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    return headers

def build_tts_body(text: str, custom_voice_setting: dict = None) -> dict:
    """构建非流式TTS请求体"""
    default_voice_setting = {
        "voice_id":"male-qn-qingse",
        "speed":1.0,
        "vol":1.0,
        "pitch":0
    }

    voice_setting = default_voice_setting.copy()
    if custom_voice_setting is not None:
        voice_setting.update(custom_voice_setting)

    body = {
        "model":"speech-02-hd",

        "text":text,
        "stream":False,
        "voice_setting":voice_setting,
        "audio_setting":{
            "sample_rate":32000,
            "bitrate":128000,
            "format":"mp3",
            "channel":1
        },
        "language_boost":"Chinese"
    }
    return body



def call_tts_non_stream(text: str, custom_voice_setting: dict = None) -> bytes:
    """非流式TTS请求 - 基于MiniMax官方示例"""

    url = "https://api.minimax.chat/v1/t2a_v2?GroupId=" + group_id
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    tts_body = build_tts_body(text, custom_voice_setting)

    # logger.info(f"发送MiniMax非流式TTS请求: {url}")
    # logger.info(f"请求头: {headers}")
    # logger.info(f"请求体: {tts_body}")

    try:
        # 发送非流式请求
        response = requests.request(
            "POST",
            url,
            stream=True,  #官方非流式的示例此处为True，应该不是指流式的意思
            headers=headers,
            data=json.dumps(tts_body),
        )

        logger.info(f"MiniMax响应状态码: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"MiniMax API请求失败: {response.status_code} - {response.text}")
            return b""

        # 解析响应
        try:
            parsed_json = json.loads(response.text)
            logger.info(f"MiniMax响应JSON: {parsed_json}")

            # 检查错误状态
            if "base_resp" in parsed_json:
                base_resp = parsed_json["base_resp"]
                if base_resp.get("status_code", 0) != 0:
                    logger.error(f"MiniMax API错误: {base_resp}")
                    return b""

            # 获取音频数据
            if "data" in parsed_json and parsed_json["data"] is not None:
                if "audio" in parsed_json["data"]:
                    audio_hex = parsed_json["data"]["audio"]
                    if audio_hex:
                        # 将hex字符串转换为字节
                        audio_bytes = bytes.fromhex(audio_hex)
                        logger.info(f"成功获取音频数据: hex长度={len(audio_hex)}, 字节长度={len(audio_bytes)}")
                        return audio_bytes
                    else:
                        logger.error("MiniMax返回的音频数据为空")
                        return b""
                else:
                    logger.error("MiniMax响应中没有audio字段")
                    return b""
            else:
                logger.error("MiniMax响应中没有data字段或data为空")
                return b""

        except json.JSONDecodeError as e:
            logger.error(f"MiniMax响应JSON解析失败: {e}")
            # logger.error(f"响应内容: {response.text[:500]}...")
            return b""
        except ValueError as e:
            logger.error(f"音频hex解码失败: {e}")
            return b""

    except requests.exceptions.Timeout:
        logger.error("MiniMax API请求超时")
        return b""
    except requests.exceptions.RequestException as e:
        logger.error(f"MiniMax API请求异常: {e}")
        return b""
    except Exception as e:
        logger.error(f"MiniMax非流式处理异常: {e}")
        return b""


def audio_play(audio_stream: Iterator[bytes]) -> bytes:
    audio = b""
    chunk_count = 0
    total_hex_length = 0

    logger.info("开始组装音频数据...")

    for chunk in audio_stream:
        if chunk is not None and chunk != '\n':
            try:
                chunk_count += 1
                total_hex_length += len(chunk)
                decoded_hex = bytes.fromhex(chunk)
                audio += decoded_hex
                logger.debug(f"处理音频chunk {chunk_count}: hex长度={len(chunk)}, 解码后长度={len(decoded_hex)}")
            except ValueError as e:
                logger.error(f"音频chunk {chunk_count} hex解码失败: {e}, chunk内容: {chunk[:100]}...")
                continue
            except Exception as e:
                logger.error(f"处理音频chunk {chunk_count} 时发生错误: {e}")
                continue

    logger.info(f"音频数据组装完成: 处理了{chunk_count}个chunk, 总hex长度={total_hex_length}, 最终音频大小={len(audio)}字节")

    if len(audio) == 0:
        logger.error("警告: 没有获取到任何音频数据!")

    return audio


def minimax_request(text, save_folder, custom_voice_setting:dict=None, output_filename=None):
    """
    调用MiniMax API生成语音

    参数:
    text (str): 要转换为语音的文本
    save_folder (str): 保存结果的文件夹路径
    custom_voice_setting (dict, optional): 自定义语音设置
    output_filename (str, optional): 指定输出文件名，不包含路径和扩展名

    返回:
    str: 生成的音频文件路径
    """
    import subprocess

    # 使用非流式请求
    audio = call_tts_non_stream(text, custom_voice_setting)

    # 检查音频数据是否为空
    if not audio or len(audio) == 0:
        logger.error("MiniMax API返回的音频数据为空")
        raise Exception("Failed to generate audio: empty response from MiniMax API")

    # 确保保存文件夹存在
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)

    # 生成文件名
    if output_filename:
        file_name = f'{save_folder}/{output_filename}.{file_format}'
    else:
        timestamp = int(time.time())
        file_name = f'{save_folder}/output_total_{timestamp}.{file_format}'

    # 保存原始音频文件
    raw_file_name = file_name.replace('.mp3', '_raw.mp3')
    with open(raw_file_name, 'wb') as file:
        file.write(audio)

    logger.info(f"Raw audio saved to {raw_file_name}, size: {len(audio)} bytes")

    # 验证并修复音频文件
    try:
        # 使用FFmpeg重新编码音频文件以确保兼容性
        # 使用与最终视频合并兼容的参数
        ffmpeg_command = [
            'ffmpeg',
            '-i', raw_file_name,
            '-acodec', 'mp3',
            '-ar', '44100',  # 标准采样率，更好的兼容性
            '-ab', '192k',   # 与最终视频合并一致的比特率
            '-ac', '1',      # 单声道，适合语音
            '-y',            # 覆盖输出文件
            file_name
        ]

        logger.info(f"重新编码音频文件: {' '.join(ffmpeg_command)}")
        result = subprocess.run(ffmpeg_command, capture_output=True, text=True, check=True)
        logger.info(f"音频重新编码成功: {file_name}")

        # 删除原始文件
        os.remove(raw_file_name)

    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg重新编码失败: {e.stderr}")
        # 如果重新编码失败，使用原始文件
        os.rename(raw_file_name, file_name)
        logger.warning(f"使用原始音频文件: {file_name}")
    except Exception as e:
        logger.error(f"音频处理过程中发生错误: {str(e)}")
        # 如果处理失败，使用原始文件
        if os.path.exists(raw_file_name):
            os.rename(raw_file_name, file_name)

    logger.info(f"Audio saved to {file_name}")
    return file_name

if __name__ == "__main__":
    minimax_request("这是一个示例音频","test61")