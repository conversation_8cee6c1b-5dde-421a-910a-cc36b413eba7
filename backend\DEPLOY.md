# 在Fly.io上部署FastAPI + Celery + Redis应用

本文档提供了在Fly.io上部署FastAPI应用和Celery Worker的详细指南。

## 架构概述

我们的部署架构包括以下组件：

1. **FastAPI应用** - 处理HTTP请求，提交任务到Celery
2. **Celery Worker** - 处理后台任务
3. **Redis服务** - 作为消息代理和结果后端
4. **共享存储** - 用于存储上传的文件和生成的结果

## 前提条件

1. 安装Fly.io CLI工具：
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```

2. 登录Fly.io：
   ```bash
   flyctl auth login
   ```

3. 确保你有一个Fly.io Redis服务，或者其他可访问的Redis服务。

## 部署步骤

### 1. 创建Fly.io应用

如果你还没有创建应用，可以使用以下命令创建：

```bash
# 创建API应用
flyctl apps create aipresenter-api

# 创建Worker应用
flyctl apps create aipresenter-worker
```

### 2. 创建共享存储卷

为了在多个实例之间共享文件，我们需要创建Fly.io卷：

```bash
# 为API应用创建卷
flyctl volumes create aipresenter_data --size 10 --app aipresenter-api

# 为Worker应用创建卷
flyctl volumes create aipresenter_data --size 10 --app aipresenter-worker
```

### 3. 准备环境变量

有两种方式设置环境变量：

#### 方式一：使用.env文件（推荐）

在`backend`目录下创建或编辑`.env`文件，包含以下内容：

```
REDIS_URL=redis://your-redis-url:6379
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
GROUP_ID=your-group-id
MINIMAX_APIKEY=your-minimax-apikey
```

部署脚本会自动从这个文件读取环境变量。

#### 方式二：手动设置环境变量

如果你不想使用`.env`文件，也可以手动设置环境变量：

```bash
# 为API应用设置环境变量
flyctl secrets set --app aipresenter-api \
    REDIS_URL="redis://your-redis-url:6379" \
    SUPABASE_URL="your-supabase-url" \
    SUPABASE_ANON_KEY="your-supabase-anon-key" \
    SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key" \
    GROUP_ID="your-group-id" \
    MINIMAX_APIKEY="your-minimax-apikey"

# 为Worker应用设置环境变量
flyctl secrets set --app aipresenter-worker \
    REDIS_URL="redis://your-redis-url:6379" \
    SUPABASE_URL="your-supabase-url" \
    SUPABASE_ANON_KEY="your-supabase-anon-key" \
    SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key" \
    GROUP_ID="your-group-id" \
    MINIMAX_APIKEY="your-minimax-apikey"
```

### 4. 部署应用

使用提供的部署脚本：

```bash
# 赋予脚本执行权限
chmod +x deploy.sh

# 使用.env文件部署API和Worker应用
./deploy.sh -r "redis://your-redis-url:6379"

# 指定不同的.env文件
./deploy.sh -r "redis://your-redis-url:6379" -e ".env.production"

# 或者单独部署
./deploy.sh -a -r "redis://your-redis-url:6379"  # 仅部署API
./deploy.sh -w -r "redis://your-redis-url:6379"  # 仅部署Worker
```

部署脚本会自动从`.env`文件读取环境变量，你只需要提供Redis URL即可。

或者手动部署：

```bash
# 部署API应用
flyctl deploy --config fly.api.toml

# 部署Worker应用
flyctl deploy --config fly.worker.toml
```

### 5. 验证部署

检查应用状态：

```bash
# 检查API应用状态
flyctl status --app aipresenter-api

# 检查Worker应用状态
flyctl status --app aipresenter-worker
```

查看日志：

```bash
# 查看API应用日志
flyctl logs --app aipresenter-api

# 查看Worker应用日志
flyctl logs --app aipresenter-worker
```

## 故障排除

### 1. Redis连接问题

如果遇到Redis连接问题，请检查：

- Redis URL是否正确
- Redis服务是否运行
- 网络连接是否正常

### 2. 文件存储问题

如果遇到文件存储问题，请检查：

- 卷是否正确挂载
- 应用是否有写入权限
- 文件路径是否正确

### 3. 应用崩溃

如果应用崩溃，请检查日志：

```bash
flyctl logs --app aipresenter-api
```

## 扩展应用

如果需要扩展应用，可以增加实例数量：

```bash
# 扩展API应用
flyctl scale count 2 --app aipresenter-api

# 扩展Worker应用
flyctl scale count 3 --app aipresenter-worker
```

## 更新应用

要更新应用，只需重新部署：

```bash
./deploy.sh
```

## 注意事项

1. **文件存储**：Fly.io的卷是区域性的，如果你的应用在多个区域运行，需要考虑使用Supabase存储或其他云存储服务。

2. **Redis持久化**：确保Redis配置了持久化，以防止数据丢失。

3. **监控**：考虑设置监控和告警，以便及时发现问题。

4. **备份**：定期备份重要数据。
