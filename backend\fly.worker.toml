# fly.toml app configuration file for Celery Worker
app = "aipresenter-worker"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.worker"

[env]
  # Redis配置
  REDIS_URL = "redis://default:<EMAIL>:6379"

  # 其他环境变量将从.env文件读取或通过 flyctl secrets set 设置

# Worker进程配置
[processes]
  app = "bash -c 'cd /app && python -m app.worker_health & python -m celery -A app.celery_worker worker --loglevel=debug --concurrency=2'"

# 添加HTTP服务配置，用于健康检查
[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[vm]]
  memory = "2gb"
  cpu_kind = "shared"
  cpus = 2

#[mounts]
 # source = "aipresenter_data"
 # destination = "/app/data"
