#!/usr/bin/env python3
"""
MiniMax API 本地测试脚本
用于验证 API 密钥和 GROUP_ID 是否正确配置
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_minimax_api():
    """测试 MiniMax API 非流式请求"""
    
    # 从环境变量获取配置
    group_id = os.getenv('GROUP_ID')
    api_key = os.getenv('MINIMAX_APIKEY')
    
    print("=== MiniMax API 测试 ===")
    print(f"GROUP_ID: {group_id}")
    print(f"API_KEY: {api_key[:10]}...{api_key[-10:] if api_key else 'None'}")
    
    if not group_id or not api_key:
        print("❌ 错误: GROUP_ID 或 MINIMAX_APIKEY 环境变量未设置")
        print("请在 .env 文件中设置这些变量，或者直接在脚本中修改")
        return False
    
    # 构建请求
    url = f"https://api.minimax.chat/v1/t2a_v2?GroupId={group_id}"
    
    payload = {
        "model": "speech-02-turbo",
        "text": "这是一个测试文本，用于验证MiniMax API是否正常工作。",
        "stream": False,
        "voice_setting": {
            "voice_id": "male-qn-qingse",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0
        },
        "audio_setting": {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3",
            "channel": 1
        },
        "language_boost": "Chinese"
    }
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    print(f"\n📡 发送请求到: {url}")
    print(f"📝 请求体: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    
    try:
        # 发送请求
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=60)
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ HTTP 错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        # 解析响应
        try:
            parsed_json = response.json()
            print(f"📄 响应JSON: {json.dumps(parsed_json, indent=2, ensure_ascii=False)}")
            
            # 检查错误状态
            if "base_resp" in parsed_json:
                base_resp = parsed_json["base_resp"]
                status_code = base_resp.get("status_code", 0)
                status_msg = base_resp.get("status_msg", "")
                
                if status_code != 0:
                    print(f"❌ MiniMax API 错误:")
                    print(f"   状态码: {status_code}")
                    print(f"   错误信息: {status_msg}")
                    
                    # 提供错误码说明
                    error_codes = {
                        1000: "未知错误",
                        1001: "超时",
                        1002: "触发限流",
                        1004: "鉴权失败 - API密钥或GROUP_ID不匹配",
                        1039: "触发TPM限流",
                        1042: "非法字符超过10%",
                        2013: "输入格式信息不正常"
                    }
                    
                    if status_code in error_codes:
                        print(f"   说明: {error_codes[status_code]}")
                    
                    return False
            
            # 检查音频数据
            if "data" in parsed_json and parsed_json["data"] is not None:
                if "audio" in parsed_json["data"]:
                    audio_hex = parsed_json["data"]["audio"]
                    if audio_hex:
                        audio_bytes = bytes.fromhex(audio_hex)
                        print(f"✅ 成功获取音频数据:")
                        print(f"   Hex 长度: {len(audio_hex)} 字符")
                        print(f"   音频大小: {len(audio_bytes)} 字节")
                        
                        # 保存测试音频文件
                        output_file = "test_output.mp3"
                        with open(output_file, 'wb') as f:
                            f.write(audio_bytes)
                        print(f"   已保存到: {output_file}")
                        
                        return True
                    else:
                        print("❌ 音频数据为空")
                        return False
                else:
                    print("❌ 响应中没有 audio 字段")
                    return False
            else:
                print("❌ 响应中没有 data 字段或 data 为空")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON 解析失败: {e}")
            print(f"响应内容: {response.text[:500]}...")
            return False
        except ValueError as e:
            print(f"❌ 音频 hex 解码失败: {e}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主函数"""
    print("MiniMax API 测试脚本")
    print("=" * 50)
    
    # 如果没有 .env 文件，提示用户手动设置
    if not os.path.exists('.env'):
        print("⚠️  未找到 .env 文件")
        print("请创建 .env 文件并添加以下内容:")
        print("GROUP_ID=你的GROUP_ID")
        print("MINIMAX_APIKEY=你的API密钥")
        print("\n或者直接在下面修改代码中的变量:")
        
        # 如果需要，可以在这里直接设置
        # os.environ['GROUP_ID'] = '你的GROUP_ID'
        # os.environ['MINIMAX_APIKEY'] = '你的API密钥'
    
    success = test_minimax_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功! MiniMax API 工作正常")
    else:
        print("💥 测试失败! 请检查配置")
        print("\n🔧 故障排除建议:")
        print("1. 确认 GROUP_ID 和 MINIMAX_APIKEY 是否正确")
        print("2. 确认 API 密钥是否有效且未过期")
        print("3. 确认账户是否有足够的配额")
        print("4. 检查网络连接是否正常")

if __name__ == "__main__":
    main()
